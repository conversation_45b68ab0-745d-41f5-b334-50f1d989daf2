<style>
    .modern-form-section {
        background: #ffffff;
        border-radius: 15px;
        padding: 2rem;
        margin-bottom: 1.5rem;
        box-shadow: 0 5px 20px rgba(0, 0, 0, 0.05);
        border: 1px solid #f0f0f0;
        transition: all 0.3s ease;
    }

    .section-header {
        display: flex;
        align-items: center;
        margin-bottom: 1.5rem;
        padding-bottom: 1rem;
        border-bottom: 2px solid #f8f9fa;
    }

    .section-icon {
        width: 50px;
        height: 50px;
        border-radius: 12px;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 1rem;
        font-size: 1.5rem;
        color: white;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    }

    .section-title {
        font-size: 1.4rem;
        font-weight: 600;
        color: #2d3748;
        margin: 0;
    }

    .section-subtitle {
        font-size: 0.9rem;
        color: #718096;
        margin: 0.25rem 0 0 0;
    }

    .form-label {
        font-weight: 600;
        color: #4a5568;
        margin-bottom: 0.5rem;
        font-size: 0.95rem;
    }

    .form-control {
        border: 2px solid #e2e8f0;
        border-radius: 10px;
        padding: 0.75rem 1rem;
        font-size: 0.95rem;
        transition: all 0.3s ease;
        background-color: #f8f9fa;
    }

    .form-control:focus {
        border-color: #667eea;
        box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        background-color: #ffffff;
    }

    .form-check-input {
        width: 1.5rem;
        height: 1.5rem;
        border: 2px solid #e2e8f0;
        border-radius: 6px;
    }

    .form-check-input:checked {
        background-color: #667eea;
        border-color: #667eea;
    }

    .form-check-label {
        font-weight: 500;
        color: #4a5568;
        margin-left: 0.5rem;
    }

    .repeater-item {
        background: #f8f9fa;
        border: 2px solid #e2e8f0;
        border-radius: 12px;
        padding: 1.5rem;
        margin-bottom: 1rem;
        position: relative;
    }

    .repeater-item .remove-btn {
        position: absolute;
        top: 10px;
        right: 10px;
        background: #dc3545;
        color: white;
        border: none;
        border-radius: 50%;
        width: 30px;
        height: 30px;
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        font-size: 14px;
    }

    .add-repeater-btn {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border: none;
        border-radius: 10px;
        padding: 0.75rem 1.5rem;
        font-weight: 600;
        cursor: pointer;
        transition: all 0.3s ease;
    }

    .add-repeater-btn:hover {
        transform: translateY(-2px);
        box-shadow: 0 5px 15px rgba(102, 126, 234, 0.3);
    }

    .file-upload-area {
        border: 2px dashed #e2e8f0;
        border-radius: 12px;
        padding: 2rem;
        text-align: center;
        background: #f8f9fa;
        transition: all 0.3s ease;
        cursor: pointer;
    }

    .file-upload-area:hover {
        border-color: #667eea;
        background: #f0f4ff;
    }

    .file-upload-area.dragover {
        border-color: #667eea;
        background: #e6f3ff;
    }

    .upload-icon {
        font-size: 3rem;
        color: #a0aec0;
        margin-bottom: 1rem;
    }

    .upload-text {
        color: #4a5568;
        font-weight: 500;
        margin-bottom: 0.5rem;
    }

    .upload-hint {
        color: #718096;
        font-size: 0.85rem;
    }

    .image-preview {
        max-width: 200px;
        max-height: 150px;
        border-radius: 8px;
        margin-top: 1rem;
        border: 2px solid #e2e8f0;
    }
</style>

<div class="tab-pane fade active show" id="checkout" role="tabpanel" aria-labelledby="checkout-tab">
    <div class="modern-form-section">
        <div class="section-header">
            <div class="section-icon">
                <i class="fi-rr-shopping-cart"></i>
            </div>
            <div>
                <h3 class="section-title">Cấu hình trang Checkout</h3>
                <p class="section-subtitle">Tùy chỉnh giao diện và nội dung trang thanh toán</p>
            </div>
        </div>

        <!-- Bật/tắt trang checkout -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="form-check">
                    <input class="form-check-input" type="checkbox" name="checkout_enabled" id="checkout_enabled"
                           value="1" {{ $course_details->checkout_enabled ? 'checked' : '' }}>
                    <label class="form-check-label" for="checkout_enabled">
                        Bật trang checkout
                    </label>
                </div>
            </div>
        </div>

        <!-- Các heading -->
        <div class="row mb-3">
            <div class="col-md-6">
                <label for="checkout_heading_1" class="form-label">Tiêu đề 1</label>
                <input type="text" class="form-control" name="checkout_heading_1" id="checkout_heading_1"
                       value="{{ $course_details->checkout_heading_1 }}" placeholder="Nhập tiêu đề 1">
            </div>
            <div class="col-md-6">
                <label for="checkout_heading_2" class="form-label">Tiêu đề 2</label>
                <input type="text" class="form-control" name="checkout_heading_2" id="checkout_heading_2"
                       value="{{ $course_details->checkout_heading_2 }}" placeholder="Nhập tiêu đề 2">
            </div>
        </div>

        <div class="row mb-3">
            <div class="col-md-6">
                <label for="checkout_heading_3" class="form-label">Tiêu đề 3</label>
                <input type="text" class="form-control" name="checkout_heading_3" id="checkout_heading_3"
                       value="{{ $course_details->checkout_heading_3 }}" placeholder="Nhập tiêu đề 3">
            </div>
            <div class="col-md-6">
                <label for="checkout_last_slots" class="form-label">Số slot cuối cùng</label>
                <input type="number" class="form-control" name="checkout_last_slots" id="checkout_last_slots"
                       value="{{ $course_details->checkout_last_slots }}" placeholder="Nhập số slot" min="0">
            </div>
        </div>

        <div class="row mb-4">
            <div class="col-md-6">
                <label for="checkout_heading_4" class="form-label">Tiêu đề 4</label>
                <input type="text" class="form-control" name="checkout_heading_4" id="checkout_heading_4"
                       value="{{ $course_details->checkout_heading_4 }}" placeholder="Nhập tiêu đề 4">
            </div>
            <div class="col-md-6">
                <label for="checkout_intro_video_link" class="form-label">Link video giới thiệu (YouTube)</label>
                <input type="url" class="form-control" name="checkout_intro_video_link" id="checkout_intro_video_link"
                       value="{{ $course_details->checkout_intro_video_link }}" placeholder="https://youtube.com/watch?v=...">
            </div>
        </div>
    </div>

    <!-- Ưu đãi khóa học (Repeater) -->
    <div class="modern-form-section">
        <div class="section-header">
            <div class="section-icon">
                <i class="fi-rr-tags"></i>
            </div>
            <div>
                <h3 class="section-title">Ưu đãi khóa học</h3>
                <p class="section-subtitle">Thêm các ưu đãi đặc biệt cho khóa học</p>
            </div>
        </div>

        <div id="course-offers-container">
            @php
                $offers = $course_details->checkout_course_offers;
                if (is_string($offers)) {
                    $offers = json_decode($offers, true);
                }
                $offers = is_array($offers) ? $offers : [];
            @endphp

            @if(empty($offers))
                <div class="repeater-item" data-index="0">
                    <button type="button" class="remove-btn" onclick="removeOffer(this)">×</button>
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label class="form-label">Nhãn nổi bật</label>
                            <input type="text" class="form-control" name="checkout_course_offers[0][label]"
                                   placeholder="VD: Ưu đãi đặc biệt">
                        </div>
                        <div class="col-md-6 mb-3">
                            <label class="form-label">Nội dung</label>
                            <input type="text" class="form-control" name="checkout_course_offers[0][content]"
                                   placeholder="VD: Giảm 50% cho 100 học viên đầu tiên">
                        </div>
                    </div>
                </div>
            @else
                @foreach($offers as $index => $offer)
                    <div class="repeater-item" data-index="{{ $index }}">
                        <button type="button" class="remove-btn" onclick="removeOffer(this)">×</button>
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label class="form-label">Nhãn nổi bật</label>
                                <input type="text" class="form-control" name="checkout_course_offers[{{ $index }}][label]"
                                       value="{{ $offer['label'] ?? '' }}" placeholder="VD: Ưu đãi đặc biệt">
                            </div>
                            <div class="col-md-6 mb-3">
                                <label class="form-label">Nội dung</label>
                                <input type="text" class="form-control" name="checkout_course_offers[{{ $index }}][content]"
                                       value="{{ $offer['content'] ?? '' }}" placeholder="VD: Giảm 50% cho 100 học viên đầu tiên">
                            </div>
                        </div>
                    </div>
                @endforeach
            @endif
        </div>

        <button type="button" class="add-repeater-btn" onclick="addOffer()">
            <i class="fi-rr-plus"></i> Thêm ưu đãi
        </button>
    </div>

    <!-- Upload ảnh ưu đãi độc quyền -->
    <div class="modern-form-section">
        <div class="section-header">
            <div class="section-icon">
                <i class="fi-rr-picture"></i>
            </div>
            <div>
                <h3 class="section-title">Ảnh ưu đãi độc quyền</h3>
                <p class="section-subtitle">Tải lên hình ảnh minh họa cho ưu đãi</p>
            </div>
        </div>

        <div class="file-upload-area" onclick="document.getElementById('checkout_exclusive_offer_image').click()">
            <div class="upload-icon">
                <i class="fi-rr-cloud-upload"></i>
            </div>
            <div class="upload-text">Nhấp để chọn ảnh hoặc kéo thả vào đây</div>
            <div class="upload-hint">Định dạng: JPG, PNG, GIF. Kích thước tối đa: 5MB</div>
            <input type="file" id="checkout_exclusive_offer_image" name="checkout_exclusive_offer_image"
                   accept="image/*" style="display: none;" onchange="previewImage(this)">
        </div>

        @if($course_details->checkout_exclusive_offer_image)
            <div class="mt-3">
                <img src="{{ asset($course_details->checkout_exclusive_offer_image) }}"
                     alt="Ảnh ưu đãi hiện tại" class="image-preview" id="current-image">
            </div>
        @endif

        <div id="image-preview-container" style="display: none;">
            <img id="image-preview" class="image-preview" alt="Xem trước ảnh">
        </div>
    </div>
</div>

<script>
let offerIndex = {{ count($offers) }};

function addOffer() {
    const container = document.getElementById('course-offers-container');
    const newItem = document.createElement('div');
    newItem.className = 'repeater-item';
    newItem.setAttribute('data-index', offerIndex);

    newItem.innerHTML = `
        <button type="button" class="remove-btn" onclick="removeOffer(this)">×</button>
        <div class="row">
            <div class="col-md-6 mb-3">
                <label class="form-label">Nhãn nổi bật</label>
                <input type="text" class="form-control" name="checkout_course_offers[${offerIndex}][label]"
                       placeholder="VD: Ưu đãi đặc biệt">
            </div>
            <div class="col-md-6 mb-3">
                <label class="form-label">Nội dung</label>
                <input type="text" class="form-control" name="checkout_course_offers[${offerIndex}][content]"
                       placeholder="VD: Giảm 50% cho 100 học viên đầu tiên">
            </div>
        </div>
    `;

    container.appendChild(newItem);
    offerIndex++;
}

function removeOffer(button) {
    const container = document.getElementById('course-offers-container');
    if (container.children.length > 1) {
        button.parentElement.remove();
    }
}

function previewImage(input) {
    if (input.files && input.files[0]) {
        const file = input.files[0];

        // Kiểm tra kích thước file (5MB = 5 * 1024 * 1024 bytes)
        if (file.size > 5 * 1024 * 1024) {
            alert('Kích thước file quá lớn. Vui lòng chọn file nhỏ hơn 5MB.');
            input.value = '';
            return;
        }

        // Kiểm tra định dạng file
        const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif'];
        if (!allowedTypes.includes(file.type)) {
            alert('Định dạng file không được hỗ trợ. Vui lòng chọn file JPG, PNG hoặc GIF.');
            input.value = '';
            return;
        }

        const reader = new FileReader();
        reader.onload = function(e) {
            const previewContainer = document.getElementById('image-preview-container');
            const previewImg = document.getElementById('image-preview');
            const currentImg = document.getElementById('current-image');

            previewImg.src = e.target.result;
            previewContainer.style.display = 'block';

            if (currentImg) {
                currentImg.style.display = 'none';
            }
        };

        reader.onerror = function() {
            alert('Có lỗi xảy ra khi đọc file. Vui lòng thử lại.');
            input.value = '';
        };

        reader.readAsDataURL(file);
    }
}

// Drag and drop functionality
const uploadArea = document.querySelector('.file-upload-area');
const fileInput = document.getElementById('checkout_exclusive_offer_image');

uploadArea.addEventListener('dragover', (e) => {
    e.preventDefault();
    uploadArea.classList.add('dragover');
});

uploadArea.addEventListener('dragleave', () => {
    uploadArea.classList.remove('dragover');
});

uploadArea.addEventListener('drop', (e) => {
    e.preventDefault();
    uploadArea.classList.remove('dragover');

    const files = e.dataTransfer.files;
    if (files.length > 0) {
        const file = files[0];

        // Kiểm tra kích thước file (5MB)
        if (file.size > 5 * 1024 * 1024) {
            alert('Kích thước file quá lớn. Vui lòng chọn file nhỏ hơn 5MB.');
            return;
        }

        // Kiểm tra định dạng file
        const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif'];
        if (!allowedTypes.includes(file.type)) {
            alert('Định dạng file không được hỗ trợ. Vui lòng chọn file JPG, PNG hoặc GIF.');
            return;
        }

        // Tạo FileList object để gán cho input
        const dt = new DataTransfer();
        dt.items.add(file);
        fileInput.files = dt.files;

        previewImage(fileInput);
    }
});

// Debug form submission
$(document).ready(function() {
    $('.ajaxForm').on('submit', function(e) {
        console.log('Form submitting...');

        // Check if file is selected
        const fileInput = document.getElementById('checkout_exclusive_offer_image');
        if (fileInput && fileInput.files.length > 0) {
            console.log('File selected:', fileInput.files[0].name, 'Size:', fileInput.files[0].size);
        }
    });
});
</script>
