<div class="checkout-container">
    <div class="container">
        <div class="checkout-card">
            <!-- Header Section -->
            <div class="checkout-header">
                <h1>{{ $checkout_heading_1 ?? '<PERSON><PERSON>ng ký khóa học ngay!' }}</h1>
                <p>{{ $checkout_heading_2 ?? 'C<PERSON> hội học tập tuyệt vời đang chờ bạn' }}</p>
            </div>

            <div class="checkout-content">
                <!-- Course Information -->
                <div class="course-info">
                    <h2 class="course-title">{{ $course_details->title }}</h2>
                    @if($course_details->short_description)
                        <div class="course-description">
                            {!! $course_details->short_description !!}
                        </div>
                    @endif
                </div>

                <!-- Heading 3 Section -->
                @if($checkout_heading_3)
                    <div class="text-center mb-4">
                        <h3 style="font-size: 1.5rem; font-weight: 600; color: #2d3748;">
                            {{ $checkout_heading_3 }}
                        </h3>
                    </div>
                @endif

                <!-- Slots Warning -->
                @if($checkout_last_slots && $checkout_last_slots > 0)
                    <div class="slots-warning">
                        <i class="fas fa-exclamation-triangle"></i>
                        Chỉ còn {{ $checkout_last_slots }} suất cuối cùng!
                    </div>
                @endif

                <!-- Course Offers -->
                @if(!empty($checkout_course_offers))
                    <div class="offers-section">
                        <h3 style="text-align: center; margin-bottom: 1.5rem; color: #2d3748;">
                            {{ $checkout_heading_4 ?? 'Ưu đãi đặc biệt' }}
                        </h3>
                        @foreach($checkout_course_offers as $offer)
                            @if(!empty($offer['label']) || !empty($offer['content']))
                                <div class="offer-item">
                                    @if(!empty($offer['label']))
                                        <div class="offer-label">
                                            <i class="fas fa-star"></i> {{ $offer['label'] }}
                                        </div>
                                    @endif
                                    @if(!empty($offer['content']))
                                        <div class="offer-content">{{ $offer['content'] }}</div>
                                    @endif
                                </div>
                            @endif
                        @endforeach
                    </div>
                @endif

                <!-- Video Introduction -->
                @if($checkout_intro_video_link && isset($checkout_video_id))
                    <div class="video-section">
                        <h3 style="margin-bottom: 1rem; color: #2d3748;">Video giới thiệu khóa học</h3>
                        <div class="video-wrapper">
                            <iframe 
                                src="https://www.youtube.com/embed/{{ $checkout_video_id }}?rel=0&showinfo=0" 
                                frameborder="0" 
                                allowfullscreen>
                            </iframe>
                        </div>
                    </div>
                @endif

                <!-- Exclusive Offer Image -->
                @if($checkout_exclusive_offer_image)
                    <div class="exclusive-offer">
                        <h3 style="margin-bottom: 1rem; color: #2d3748;">Ưu đãi độc quyền</h3>
                        <img src="{{ asset($checkout_exclusive_offer_image) }}" 
                             alt="Ưu đãi độc quyền" 
                             class="img-fluid">
                    </div>
                @endif

                <!-- Pricing Section -->
                <div class="pricing-section">
                    <h3 style="margin-bottom: 1rem;">Giá khóa học</h3>
                    
                    @if($course_details->discount_flag && isset($discountPrice))
                        <div class="price-display">
                            @if($course_details->price > $discountPrice)
                                <span class="original-price">{{ number_format($course_details->price, 0, '.', '.') }}đ</span>
                            @endif
                            <span>{{ number_format($discountPrice, 0, '.', '.') }}đ</span>
                        </div>
                        
                        @if($course_details->price > $discountPrice)
                            @php
                                $discount_percent = round((($course_details->price - $discountPrice) / $course_details->price) * 100);
                            @endphp
                            <div class="discount-badge">
                                Tiết kiệm {{ $discount_percent }}%
                            </div>
                        @endif
                    @else
                        <div class="price-display">
                            @if($course_details->discounted_price && $course_details->discounted_price < $course_details->price)
                                <span class="original-price">{{ number_format($course_details->price, 0, '.', '.') }}đ</span>
                                <span>{{ number_format($course_details->discounted_price, 0, '.', '.') }}đ</span>
                            @else
                                <span>{{ number_format($course_details->price, 0, '.', '.') }}đ</span>
                            @endif
                        </div>
                        
                        @if($course_details->discounted_price && $course_details->discounted_price < $course_details->price)
                            @php
                                $discount_percent = round((($course_details->price - $course_details->discounted_price) / $course_details->price) * 100);
                            @endphp
                            <div class="discount-badge">
                                Tiết kiệm {{ $discount_percent }}%
                            </div>
                        @endif
                    @endif

                    <!-- Countdown Timer -->
                    @if(isset($countdown_date) && $countdown_date !== '1970-04-13')
                        <div class="countdown-timer">
                            <p style="margin-bottom: 0.5rem;">Ưu đãi kết thúc trong:</p>
                            <div id="countdown-display" class="timer-display">--:--:--</div>
                        </div>
                    @endif

                    <!-- Checkout Button -->
                    @if(auth()->check())
                        @if($enroll_status == 'valid')
                            <button class="checkout-btn" disabled style="background: #95a5a6;">
                                <i class="fas fa-check"></i> Đã đăng ký
                            </button>
                        @else
                            <button class="checkout-btn" onclick="window.location.href='{{ route('course.details', $course_details->slug) }}'">
                                <i class="fas fa-shopping-cart"></i> Đăng ký ngay
                            </button>
                        @endif
                    @else
                        <button class="checkout-btn" onclick="window.location.href='{{ route('login') }}'">
                            <i class="fas fa-sign-in-alt"></i> Đăng nhập để đăng ký
                        </button>
                    @endif

                    <!-- Additional Info -->
                    <div style="margin-top: 1.5rem; font-size: 0.9rem; opacity: 0.9;">
                        <p><i class="fas fa-shield-alt"></i> Thanh toán an toàn 100%</p>
                        <p><i class="fas fa-clock"></i> Truy cập trọn đời</p>
                        <p><i class="fas fa-certificate"></i> Cấp chứng chỉ hoàn thành</p>
                        @if($total_lesson)
                            <p><i class="fas fa-play-circle"></i> {{ $total_lesson }} bài học</p>
                        @endif
                    </div>
                </div>

                <!-- Course Stats -->
                <div class="row text-center mt-4">
                    @if($enroll)
                        <div class="col-md-4">
                            <div style="padding: 1rem; background: #f8f9fa; border-radius: 10px; margin-bottom: 1rem;">
                                <h4 style="color: #667eea; margin-bottom: 0.5rem;">{{ $enroll }}</h4>
                                <p style="margin: 0; color: #718096;">Học viên đã đăng ký</p>
                            </div>
                        </div>
                    @endif
                    
                    @if($total_lesson)
                        <div class="col-md-4">
                            <div style="padding: 1rem; background: #f8f9fa; border-radius: 10px; margin-bottom: 1rem;">
                                <h4 style="color: #667eea; margin-bottom: 0.5rem;">{{ $total_lesson }}</h4>
                                <p style="margin: 0; color: #718096;">Bài học</p>
                            </div>
                        </div>
                    @endif
                    
                    <div class="col-md-4">
                        <div style="padding: 1rem; background: #f8f9fa; border-radius: 10px; margin-bottom: 1rem;">
                            <h4 style="color: #667eea; margin-bottom: 0.5rem;">
                                @if($course_details->level == 'beginner')
                                    Cơ bản
                                @elseif($course_details->level == 'intermediate')
                                    Trung cấp
                                @elseif($course_details->level == 'advanced')
                                    Nâng cao
                                @else
                                    {{ $course_details->level }}
                                @endif
                            </h4>
                            <p style="margin: 0; color: #718096;">Cấp độ</p>
                        </div>
                    </div>
                </div>

                <!-- Back to Course Details -->
                <div class="text-center mt-4">
                    <a href="{{ route('course.details', $course_details->slug) }}" 
                       style="color: #667eea; text-decoration: none; font-weight: 500;">
                        <i class="fas fa-arrow-left"></i> Xem chi tiết khóa học
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>
