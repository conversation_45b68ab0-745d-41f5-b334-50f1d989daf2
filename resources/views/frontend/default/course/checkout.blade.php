@php
    $builder = App\Models\Builder_page::where('id', $course_details->builder_ids)->firstOrNew();
    $layout = !empty($course_details->builder_ids) ? 'layouts.default_shopee' : 'layouts.default';
@endphp

@extends($layout)
@push('title', $checkout_page_title ?? ($course_details->title . ' - Thanh toán'))
@push('meta')
    <meta name="description" content="{{ $checkout_meta_description ?? 'Đăng ký khóa học ' . $course_details->title }}">
@endpush

@push('css')
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/toastr.js/latest/toastr.min.css">
    <style>
        .checkout-container {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 2rem 0;
        }

        .checkout-card {
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.1);
            overflow: hidden;
            max-width: 1200px;
            margin: 0 auto;
        }

        .checkout-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 2rem;
            text-align: center;
        }

        .checkout-header h1 {
            font-size: 2.5rem;
            font-weight: 700;
            margin-bottom: 0.5rem;
        }

        .checkout-header p {
            font-size: 1.1rem;
            opacity: 0.9;
        }

        .checkout-content {
            padding: 3rem;
        }

        .course-info {
            background: #f8f9fa;
            border-radius: 15px;
            padding: 2rem;
            margin-bottom: 2rem;
        }

        .course-title {
            font-size: 1.8rem;
            font-weight: 600;
            color: #2d3748;
            margin-bottom: 1rem;
        }

        .course-description {
            color: #718096;
            line-height: 1.6;
        }

        .offers-section {
            margin: 2rem 0;
        }

        .offer-item {
            background: linear-gradient(135deg, #ffeaa7 0%, #fab1a0 100%);
            border-radius: 12px;
            padding: 1.5rem;
            margin-bottom: 1rem;
            border-left: 5px solid #e17055;
        }

        .offer-label {
            font-weight: 700;
            color: #2d3748;
            font-size: 1.1rem;
            margin-bottom: 0.5rem;
        }

        .offer-content {
            color: #4a5568;
            font-size: 1rem;
        }

        .video-section {
            text-align: center;
            margin: 2rem 0;
        }

        .video-wrapper {
            position: relative;
            width: 100%;
            height: 0;
            padding-bottom: 56.25%; /* 16:9 aspect ratio */
            border-radius: 15px;
            overflow: hidden;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
        }

        .video-wrapper iframe {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
        }

        .exclusive-offer {
            text-align: center;
            margin: 2rem 0;
        }

        .exclusive-offer img {
            max-width: 100%;
            height: auto;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
        }

        .pricing-section {
            background: linear-gradient(135deg, #74b9ff 0%, #0984e3 100%);
            color: white;
            border-radius: 15px;
            padding: 2rem;
            text-align: center;
            margin: 2rem 0;
        }

        .price-display {
            font-size: 3rem;
            font-weight: 700;
            margin: 1rem 0;
        }

        .original-price {
            text-decoration: line-through;
            opacity: 0.7;
            font-size: 1.5rem;
            margin-right: 1rem;
        }

        .discount-badge {
            background: #e74c3c;
            color: white;
            padding: 0.5rem 1rem;
            border-radius: 25px;
            font-weight: 600;
            display: inline-block;
            margin: 1rem 0;
        }

        .slots-warning {
            background: #ff7675;
            color: white;
            padding: 1rem;
            border-radius: 10px;
            text-align: center;
            font-weight: 600;
            margin: 1rem 0;
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.7; }
        }

        .checkout-btn {
            background: linear-gradient(135deg, #00b894 0%, #00a085 100%);
            color: white;
            border: none;
            padding: 1rem 3rem;
            font-size: 1.2rem;
            font-weight: 600;
            border-radius: 50px;
            cursor: pointer;
            transition: all 0.3s ease;
            width: 100%;
            margin-top: 1rem;
        }

        .checkout-btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 10px 25px rgba(0, 184, 148, 0.3);
        }

        .countdown-timer {
            background: #2d3748;
            color: white;
            padding: 1rem;
            border-radius: 10px;
            text-align: center;
            margin: 1rem 0;
        }

        .timer-display {
            font-size: 1.5rem;
            font-weight: 700;
            font-family: 'Courier New', monospace;
        }

        @media (max-width: 768px) {
            .checkout-content {
                padding: 1.5rem;
            }

            .checkout-header h1 {
                font-size: 2rem;
            }

            .price-display {
                font-size: 2rem;
            }
        }
    </style>
@endpush

@section('content')
    @if(!empty($course_details->builder_ids))
        @if($builder->is_permanent)
            @include('components.home_permanent_templates.'.$builder->identifier)
        @endif

        @php $builder_files = $builder->html ? json_decode($builder->html, true) : []; @endphp

        @foreach ($builder_files as $builder_file_name)
            @include('components.home_made_by_builder.'.$builder_file_name)
        @endforeach
    @else
        @include('frontend.default.course.checkout_default')
    @endif
@endsection

@push('js')
    <script src="https://cdnjs.cloudflare.com/ajax/libs/toastr.js/latest/toastr.min.js"></script>
    <script>
        // Countdown timer functionality
        @if(isset($countdown_date) && $countdown_date !== '1970-04-13')
            function updateCountdown() {
                const countdownDate = new Date("{{ $countdown_date }}").getTime();
                const now = new Date().getTime();
                const distance = countdownDate - now;

                if (distance > 0) {
                    const days = Math.floor(distance / (1000 * 60 * 60 * 24));
                    const hours = Math.floor((distance % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
                    const minutes = Math.floor((distance % (1000 * 60 * 60)) / (1000 * 60));
                    const seconds = Math.floor((distance % (1000 * 60)) / 1000);

                    document.getElementById('countdown-display').innerHTML =
                        `${days}d ${hours}h ${minutes}m ${seconds}s`;
                } else {
                    document.getElementById('countdown-display').innerHTML = "Đã hết hạn";
                }
            }

            // Update countdown every second
            setInterval(updateCountdown, 1000);
            updateCountdown(); // Initial call
        @endif

        // Smooth scroll to pricing section
        function scrollToPricing() {
            document.querySelector('.pricing-section').scrollIntoView({
                behavior: 'smooth'
            });
        }

        // Add some interactive effects
        document.addEventListener('DOMContentLoaded', function() {
            // Animate offer items on scroll
            const observerOptions = {
                threshold: 0.1,
                rootMargin: '0px 0px -50px 0px'
            };

            const observer = new IntersectionObserver(function(entries) {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        entry.target.style.opacity = '1';
                        entry.target.style.transform = 'translateY(0)';
                    }
                });
            }, observerOptions);

            // Observe offer items
            document.querySelectorAll('.offer-item').forEach(item => {
                item.style.opacity = '0';
                item.style.transform = 'translateY(20px)';
                item.style.transition = 'all 0.6s ease';
                observer.observe(item);
            });
        });
    </script>
@endpush
