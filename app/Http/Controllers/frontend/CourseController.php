<?php

namespace App\Http\Controllers\frontend;

use App\Http\Controllers\Controller;
use App\Models\Category;
use App\Models\Coupon;
use App\Models\Course;
use App\Models\Enrollment;
use App\Models\Lesson;
use App\Models\OfflinePayment;
use App\Models\Section;
use App\Models\User;
use App\Models\Wishlist;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Cookie;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Session;

class CourseController extends Controller
{
    public function index(Request $request, $category = '')
    {

        $layout = Session::has('view') ? session('view') : 'list';


        $page_data['layout'] = $layout;

        $query = Course::join('users', 'courses.user_id', '=', 'users.id')
            ->select('courses.*', 'users.name as instructor_name', 'users.email as instructor_email', 'users.photo as instructor_image')
            ->where('courses.status', 'active');

        // filter by category
        if ($category != '') {
            $category_details = Category::where('slug', $request->category)->first();
            if ($category_details->parent_id > 0) {
                $page_data['child_cat'] = $request->category;
                $query = $query->where('category_id', $category_details->id);
            } else {
                $sub_cat_id = Category::where('parent_id', $category_details->id)->pluck('id')->toArray();
                $sub_cat_id[] = $category_details->id;
                $query = $query->whereIn('category_id', $sub_cat_id);
                $page_data['parent_cat'] = $request->category;
            }
        }

        // searched courses
        if (request()->has('search')) {
            $query->where(function ($query) {
                $query->where('courses.title', 'LIKE', '%' . request()->input('search') . '%');
                $query->orWhere('courses.short_description', 'LIKE', '%' . request()->input('search') . '%');
                $query->orWhere('courses.level', 'LIKE', '%' . request()->input('search') . '%');
                $query->orWhere('courses.meta_keywords', 'LIKE', '%' . request()->input('search') . '%');
                $query->orWhere('courses.meta_description', 'LIKE', '%' . request()->input('search') . '%');
                $query->orWhere('courses.description', 'LIKE', '%' . request()->input('search') . '%');
            });
        }

        // filter by price
        if (request()->has('price')) {
            $price = request()->query('price');
            if ($price == 'paid') {
                $query = $query->where('is_paid', 1);
            } elseif ($price == 'discount') {
                $query = $query->where('discount_flag', 1);
            } elseif ($price == 'free') {
                $query = $query->where('is_paid', 0);
            }
        }

        // filter by level
        if (request()->has('level')) {
            $level = request()->query('level');
            $query = $query->where('level', $level);
        }

        // filter by language
        if (request()->has('language')) {
            $language = request()->query('language');
            $query = $query->where('language', $language);
        }

        // filter by rating
        if (request()->has('rating')) {
            $rating = request()->query('rating');
            $query = $query->where('average_rating', $rating);
        }

        $wishlist = [];
        if (isset(auth()->user()->id)) {
            $wishlist = Wishlist::where('user_id', auth()->user()->id)->pluck('course_id')->toArray();
        }

        $page_data['courses'] = $query->latest('id')->paginate($layout == 'grid' ? 9 : 5)->appends($request->query());
        $page_data['wishlist'] = $wishlist;
        $page_data['category_details'] = Category::where('slug', $category)->first();
        return view('frontend' . '.' . get_frontend_settings('theme') . '.course.index', $page_data);
    }

    public function getPriceAndNextDate($defaultPrice, array $data, $now = null)
    {
        $now = $now ? Carbon::parse($now) : Carbon::now();

        usort($data, fn($a, $b) => strtotime($a['start_date']) <=> strtotime($b['start_date']));


        if ($now->lt(Carbon::parse($data[0]['start_date']))) {
            return [
                'price' => $defaultPrice,
                'next_start_date' => $data[0]['start_date'],
                'price_next' => $data[0]['price'],
            ];
        }

        for ($i = 0; $i < count($data); $i++) {
            $start = Carbon::parse($data[$i]['start_date']);
            $nextItem = $data[$i + 1] ?? null;

            if ($nextItem) {
                $nextStart = Carbon::parse($nextItem['start_date']);
                if ($now->gte($start) && $now->lt($nextStart)) {
                    return [
                        'price' => $data[$i]['price'],
                        'next_start_date' => $nextItem['start_date'],
                        'price_next' => $nextItem['price'],
                    ];
                }
            } else {
                if ($now->gte($start)) {
                    return [
                        'price' => $data[$i]['price'],
                        'next_start_date' => null,
                        'price_next' => null,
                    ];
                }
            }
        }

        return [
            'price' => $defaultPrice,
            'next_start_date' => null,
            'price_next' => null,
        ];
    }

    public function course_details(Request $request, $slug)
    {

        // validate slug
        if (empty($slug)) {
            return redirect()->back();
        }

        // Get payment gateway information
        $payment_gateway = DB::table('payment_gateways')->where('identifier', 'sepay')->first();
        $page_data = [];

        if ($payment_gateway) {
            $page_data['payment_gateway'] = json_decode(json_encode($payment_gateway), true);

            // Parse the keys JSON and add individual values to $page_data
            if (isset($page_data['payment_gateway']['keys'])) {
                $keys = json_decode($page_data['payment_gateway']['keys'], true);

                if ($keys) {
                    $page_data['account_name'] = $keys['name'] ?? null;
                    $page_data['bank_code'] = $keys['bank'] ?? null;
                    $page_data['account_number'] = $keys['account_number'] ?? null;
                }
            }
        }

        // course details
        $course = Course::where('slug', $slug);
        if ($course->exists()) {
            $course_details = $course->first();
            $page_data['course_details'] = $course_details;


            // Calculate enrollment status more efficiently
            if (auth()->check()) {
                $user_id = auth()->user()->id;
                $enroll_status = enroll_status($course_details->id, $user_id);
                $enrollment_status = (auth()->user()->role == 'admin' || $enroll_status == 'valid');
            } else {
                $enroll_status = 'invalid';
                $enrollment_status = false;
            }
            $enrollment_status = false;
            $page_data['enroll_status'] = $enroll_status;
            $page_data['enrollment_status'] = $enrollment_status;

            // Process coupon information
            $this->processCouponInfo($request, $course_details, $page_data);

            // Get sections and lesson information
            $page_data['sections'] = Section::where('course_id', $course_details->id)->orderBy('sort')->get();

            // Process section data with durations and video counts
            $page_data['processed_sections'] = $this->processSectionsData($page_data['sections']);

            $page_data['total_lesson'] = Lesson::where('course_id', $course_details->id)->count();
            $page_data['enroll'] = Enrollment::where('course_id', $course_details->id)->count('user_id');

            // Handle offline payment data
            $offline_payment = null;
            if (auth()->check()) {
                $offline_payment = OfflinePayment::where('course_id', $course_details->id)
                    ->where("user_id", auth()->user()->id)
                    ->where("item_type", "course")
                    ->whereIn("status", [0, 1])
                    ->orderBy("id", "desc")
                    ->first();
            }
            $page_data['offline_payment'] = $offline_payment;

            // Add price and description for QR code generation
            if ($offline_payment) {
                $page_data['total_amount'] = $offline_payment->total_amount; // Tổng tiền cần thanh toán
                $page_data['transaction_content'] = $offline_payment->transaction_content;
            }

            // Process pricing information
            $this->processPricingInfo($course_details, $page_data);

            $defaultPrice = $course_details->discounted_price;
            if (!$defaultPrice) {
                $defaultPrice = $course_details->price;
            }
            $page_data['discountPrice'] = $defaultPrice;
            $page_data['salePrice'] = ($course_details->price - $defaultPrice) * 100 / $course_details->price;

            // Kiểm tra xem đây có phải là trang checkout không
            $is_checkout = $request->route()->getName() === 'course.details_checkout';
            $page_data['is_checkout'] = $is_checkout;

            // Thêm dữ liệu checkout nếu cần
            if ($is_checkout) {
                $this->processCheckoutData($course_details, $page_data);
            }

            // Xác định view path
            if ($is_checkout) {
                $view_path = 'frontend.' . get_frontend_settings('theme') . '.course.checkout';
            } else {
                $view_path = 'frontend.' . get_frontend_settings('theme') . '.course.course_details';
            }

            if ($course_details->discount_flag == 1) {
                $price_trends = $course_details->price_trends;

                // Xử lý price_trends có thể là string hoặc array
                if (is_string($price_trends)) {
                    $price_trends = json_decode($price_trends, true);
                }

                if (!empty($price_trends)) {
                    $priceAndNextDate = $this->getPriceAndNextDate($defaultPrice, $price_trends);

                    $page_data['discountPrice'] = $priceAndNextDate["price"];
                    $page_data['salePrice'] = ($course_details->price - $priceAndNextDate["price"]) * 100 / $course_details->price;

                    $page_data['next_date_up_price'] = Carbon::parse($priceAndNextDate["next_start_date"])->format("d/m/Y");

                    $page_data['price_next'] = $priceAndNextDate["price_next"];
                }
            }

            return view($view_path, $page_data);
        } else {
            return redirect()->back();
        }
    }

    /**
     * Process coupon information for a course
     *
     * @param Request $request
     * @param Course $course_details
     * @param array $page_data
     * @return void
     */
    private function processCouponInfo(Request $request, $course_details, &$page_data)
    {

        // Initialize coupon variable
        $coupon = null;
        $discount = 0;

        $affiliate_ref_id = $this->getAffiliaterRefId();
        // Check for manual coupon entry
        if ($request->has('coupon') && !empty($request->get('coupon'))) {
            $code = $request->query('coupon');
            Session::flash('show_payment_modal', true);
            $coupon = Coupon::where('code', $code)
                ->whereIn("status", [1, 2])
                ->where(function ($query) {
                    $query->where("quantity", ">", 0)
                        ->orWhereNull("quantity");
                })
                ->where("expiry", ">", time())
                ->where(function ($query) use ($course_details) {
                    $query->where("course_id", $course_details->id)
                        ->orWhereNull("course_id");
                })
                ->first();

            if (!$coupon) {
                $page_data['message_coupon'] = "Mã coupon không tồn tại";
            } else {
                if ($coupon->user_aff_id) {
//            check xem id user ở cookie có trung với aff_user_id hay không
                    if ($affiliate_ref_id != $coupon->user_aff_id) {
                        $page_data['message_coupon'] = "Mã coupon không hợp lệ";
                    } else {
                        $discount = $coupon->discount;
                        $page_data['coupon_affiliate'] = $coupon;
                    }
                } else {
                    $discount = $coupon->discount;
                    $page_data['coupon_affiliate'] = $coupon;
                }
            }
        } else {
            // Check for affiliate coupon
            $coupon = Coupon::whereNotNull('user_aff_id')
                ->whereIn("status", [1, 2])
                ->where(function ($query) {
                    $query->where("quantity", ">", 0)
                        ->orWhereNull("quantity");
                })
                ->where("expiry", ">", time())
                ->where(function ($query) use ($course_details) {
                    $query->where("course_id", $course_details->id)
                        ->orWhereNull("course_id");
                })
                ->first();

            if ($coupon) {
                if ($coupon->user_aff_id) {
//            check xem id user ở cookie có trung với aff_user_id hay không
                    if ($affiliate_ref_id != $coupon->user_aff_id) {
                        $page_data['message_coupon'] = "Mã coupon không hợp lệ";
                    } else {
                        $discount = $coupon->discount;
                        $page_data['coupon_affiliate'] = $coupon;
                    }
                } else {
                    $discount = $coupon->discount;
                    $page_data['coupon_affiliate'] = $coupon;
                }
            }
        }

        // Get all valid coupons for this course
        $coupons = Coupon::where(function ($query) use ($course_details) {
            $query->where('course_id', $course_details->id)
                ->orWhereNull('course_id');
        })
            ->where("expiry", ">", time())
            ->where('status',1)
            ->where(function ($query) {
                $query->where("quantity", ">", 0)
                    ->orWhereNull("quantity");
            })
            ->get();
        $arr = [];
        foreach ($coupons as $cou) {
            if ($cou->user_aff_id) {
//            check xem id user ở cookie có trung với aff_user_id hay không
                if ($affiliate_ref_id == $coupon->user_aff_id) {
                    $arr[] = $cou->code;
                }
            } else {
                $arr[] = $cou->code;
            }
        }


        $page_data['discount'] = $discount;
        $page_data['discount_coupon'] = $course_details->discounted_price * ($discount / 100);
        $page_data['coupons'] = $arr;
    }

    private function getAffiliaterRefId()
    {
        if (!Cookie::has("affiliate_ref")) {
            return null;
        }
        $affiliate_ref = Cookie::get("affiliate_ref");
        $affiliate_ref = hex2bin($affiliate_ref);
        return str_replace("KH-", "", $affiliate_ref);
    }

    /**
     * Process sections data to include durations and video counts
     *
     * @param Collection $sections
     * @return array
     */
    private function processSectionsData($sections)
    {
        $processed_sections = [];

        foreach ($sections as $section) {
            $lessons = Lesson::where('section_id', $section->id)
                ->orderBy('sort')
                ->get();

            $total_duration = 0;
            $video_count = 0;

            // Pre-process all lesson durations at once to avoid multiple calls in the template
            $lesson_durations = [];

            foreach ($lessons as $lesson) {
                $duration = lesson_durations($lesson->id);
                $lesson_durations[$lesson->id] = $duration;

                if ($duration != '00:00:00') {
                    $time_parts = explode(':', $duration);
                    if (count($time_parts) == 3) {
                        $total_duration += ($time_parts[0] * 3600) + ($time_parts[1] * 60) + $time_parts[2];
                    }
                }

                if (in_array($lesson->lesson_type, ['video-url', 'system-video', 'vimeo-url', 'google_drive', 'iframe'])) {
                    $video_count++;
                }
            }

            $hours = floor($total_duration / 3600);
            $minutes = floor(($total_duration % 3600) / 60);
            $formatted_duration = sprintf("%02d:%02d", $hours, $minutes);

            $processed_sections[] = [
                'section' => $section,
                'lessons' => $lessons,
                'video_count' => $video_count,
                'formatted_duration' => $formatted_duration,
                'lesson_durations' => $lesson_durations
            ];
        }

        return $processed_sections;
    }

    /**
     * Process pricing information for a course
     *
     * @param Course $course_details
     * @param array $page_data
     * @return void
     */
    private function processPricingInfo($course_details, &$page_data)
    {
        $has_price_trends = false;
        $countdown_date = "1970-04-13"; // Default fallback date
        $current_tier_index = 0;
        $price_trends = null;
        $current_stage = "";

        if (isset($course_details->discount_flag) && $course_details->discount_flag == 1) {
            $price_trends_data = $course_details->price_trends;

            // Xử lý price_trends có thể là string hoặc array
            if (is_string($price_trends_data)) {
                $price_trends_data = json_decode($price_trends_data, true);
            }

            // Đảm bảo $price_trends_data là array
            if (!is_array($price_trends_data)) {
                $price_trends_data = [];
            }

            $has_price_trends = isset($price_trends_data) &&
                is_array($price_trends_data) &&
                !empty($price_trends_data);
        }

        if ($has_price_trends) {
            $price_trends = $price_trends_data;
            // Find the current active tier based on today's date
            $today = date('Y-m-d');

            for ($i = 0; $i < count($price_trends); $i++) {
                if ($i < count($price_trends) - 1) {
                    $current_start = $price_trends[$i]['start_date'];
                    $next_start = $price_trends[$i + 1]['start_date'];

                    if ($today >= $current_start && $today < $next_start) {
                        $current_tier_index = $i;
                        $countdown_date = $next_start;

                        if ($i === 0) {
                            $current_stage = "PRE-SELL";
                        } elseif ($i === count($price_trends) - 2) {
                            $current_stage = "LAUNCHING";
                        } else {
                            $current_stage = "GROWTH";
                        }
                        break;
                    }
                } else if ($i == count($price_trends) - 1 && $today >= $price_trends[$i]['start_date']) {
                    // Last tier with no expiration
                    $current_tier_index = $i;
                    $current_stage = "GROWTH";
                    break;
                }
            }

            // If we're before the first tier, use the first tier's start date
            if (!empty($price_trends) && $today < $price_trends[0]['start_date']) {
                $countdown_date = $price_trends[0]['start_date'];
                $current_stage = "PRE-SELL";
            }

            // Process each price tier with formatted information
            $processed_tiers = [];
            $count = 1;
            $regular_price = !empty($price_trends) ? $price_trends[count($price_trends) - 1]['price'] : 0;

            foreach ($price_trends as $index => $tier) {
                $is_active = false;
                $discount_percent = 0;
                $badge_class = "";
                $badge_text = "";

                // Determine if this tier is currently active
                if ($index < count($price_trends) - 1) {
                    $next_start = $price_trends[$index + 1]['start_date'];
                    $is_active = ($today >= $tier['start_date'] && $today < $next_start);
                } else {
                    $is_active = ($today >= $tier['start_date']);
                }

                if ($index === 0) {
                    $badge_class = "pre-sell";
                    $badge_text = "PRE-SELL";
                    $discount_percent = round((1 - ($tier['price'] / $regular_price)) * 100);
                } elseif ($index === count($price_trends) - 1) {
                    $badge_class = "growth";
                    $badge_text = "GROWTH";
                } else {
                    $badge_class = "launching";
                    $badge_text = "LAUNCHING";
                    $discount_percent = round((1 - ($tier['price'] / $regular_price)) * 100);
                }

                $formatted_price = number_format($tier['price'], 0, '.', '.');
                $date_parts = explode('-', $tier['start_date']);
                $formatted_date = isset($date_parts[2]) ? $date_parts[2] . '/' . $date_parts[1] . '/' . $date_parts[0] : '';

                $processed_tiers[] = [
                    'index' => $count++,
                    'is_active' => $is_active,
                    'start_date' => $tier['start_date'],
                    'badge_class' => $badge_class,
                    'badge_text' => $badge_text,
                    'discount_percent' => $discount_percent,
                    'formatted_price' => $formatted_price,
                    'formatted_date' => $formatted_date,
                    'is_last_tier' => ($index === count($price_trends) - 1)
                ];
            }

            $page_data['processed_tiers'] = $processed_tiers;
            $page_data['current_stage'] = $current_stage;
        }

        // For fixed pricing (no price trends)
        $original_price = $course_details->price;
        $sale_price = $course_details->discounted_price;
        $discount_percent = $original_price > 0 ? round((1 - ($sale_price / $original_price)) * 100) : 0;

        $page_data['has_price_trends'] = $has_price_trends;
        $page_data['countdown_date'] = $countdown_date;
        $page_data['price_trends'] = $price_trends;
        $page_data['current_tier_index'] = $current_tier_index;
        $page_data['original_price'] = $original_price;
        $page_data['sale_price'] = $sale_price;
        $page_data['discount_percent'] = $discount_percent;
        $page_data['formatted_original_price'] = number_format($original_price, 0, '.', '.');
        $page_data['formatted_sale_price'] = number_format($sale_price, 0, '.', '.');
        $page_data['savings'] = number_format($original_price - $sale_price, 0, '.', '.');
    }

    /**
     * Process checkout specific data
     *
     * @param Course $course_details
     * @param array $page_data
     * @return void
     */
    private function processCheckoutData($course_details, &$page_data)
    {
        // Lấy dữ liệu cấu hình checkout
        $page_data['checkout_enabled'] = $course_details->checkout_enabled ?? true;
        $page_data['checkout_heading_1'] = $course_details->checkout_heading_1;
        $page_data['checkout_heading_2'] = $course_details->checkout_heading_2;
        $page_data['checkout_heading_3'] = $course_details->checkout_heading_3;
        $page_data['checkout_last_slots'] = $course_details->checkout_last_slots;
        $page_data['checkout_heading_4'] = $course_details->checkout_heading_4;
        $page_data['checkout_intro_video_link'] = $course_details->checkout_intro_video_link;
        $page_data['checkout_exclusive_offer_image'] = $course_details->checkout_exclusive_offer_image;

        // Xử lý course offers
        $checkout_offers = $course_details->getCheckoutOffersData();
        $page_data['checkout_course_offers'] = $checkout_offers;

        // Xử lý YouTube video ID nếu có link
        if ($course_details->checkout_intro_video_link) {
            $video_id = $this->extractYouTubeVideoId($course_details->checkout_intro_video_link);
            $page_data['checkout_video_id'] = $video_id;
        }

        // Thêm thông tin bổ sung cho checkout
        $page_data['checkout_page_title'] = $course_details->title . ' - Thanh toán';
        $page_data['checkout_meta_description'] = 'Đăng ký khóa học ' . $course_details->title . ' với ưu đãi đặc biệt';
    }

    /**
     * Extract YouTube video ID from URL
     *
     * @param string $url
     * @return string|null
     */
    private function extractYouTubeVideoId($url)
    {
        $pattern = '/(?:youtube\.com\/(?:[^\/]+\/.+\/|(?:v|e(?:mbed)?)\/|.*[?&]v=)|youtu\.be\/)([^"&?\/\s]{11})/';
        preg_match($pattern, $url, $matches);
        return isset($matches[1]) ? $matches[1] : null;
    }

    public function change_layout(Request $request)
    {
        $layout = Session::has('view');
        if ($layout) {
            Session::forget('view');
        }
        session(['view' => $request->view]);
        return response()->json(['reload' => true]);
    }

    // ------------------------------------------------------------------------------------------------------

    public function compare(Request $request)
    {
        $course_1 = $request->course_1;
        $course_2 = $request->course_2;
        $course_3 = $request->course_3;

        if (isset($course_1) && $course_1 != '') {
            $course_details_1 = Course::where('status', 'active')->where('slug', $course_1)->first();
            $page_data['course_details_1'] = $course_details_1;
        }
        if (isset($course_2) && $course_2 != '') {
            $course_details_2 = Course::where('status', 'active')->where('slug', $course_2)->first();
            $page_data['course_details_2'] = $course_details_2;
        }
        if (isset($course_3) && $course_3 != '') {
            $course_details_3 = Course::where('status', 'active')->where('slug', $course_3)->first();
            $page_data['course_details_3'] = $course_details_3;
        }

        if ($course_1 == '' && $course_2 == '' && $course_3 == '') {
            $page_data['course_details'] = '';
        }

        return view('frontend.course.compare', $page_data);
    }

    public function comparewith($course_1 = '', $course_2 = '', $course_3 = '')
    {

        $response = array();
        $result = Course::join('users', 'courses.user_id', 'users.id')
            ->select('course.id as id', 'course.title as title', 'users.first_name as first_name', 'users.last_name as last_name')
            ->where('course.id', '!=', $course_1)
            ->where('course.id', '!=', $course_2)
            ->where('course.id', '!=', $course_3)
            ->where('course.status', 'active')
            ->like('course.title', $_GET['searchVal'])
            ->or_like('short_description', $_GET['searchVal'])
            ->or_like('first_name', $_GET['searchVal'])
            ->or_like('last_name', $_GET['searchVal'])
            ->take(100)
            ->get();

        foreach ($result as $key => $row) {
            $response[] = ['id' => $row->id, 'text' => $row->title . ' (' . get_phrase('Creator') . ': ' . $row->first_name];
        }
        echo json_encode($response);
    }
}
