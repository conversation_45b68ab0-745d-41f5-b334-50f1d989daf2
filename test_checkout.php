<?php

require_once 'vendor/autoload.php';

$app = require_once 'bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

$course = App\Models\Course::first();

if ($course) {
    echo "Course ID: " . $course->id . "\n";
    echo "Course offers (raw): ";
    var_dump($course->checkout_course_offers);
    echo "\nMethod result: ";
    var_dump($course->getCheckoutOffersData());
} else {
    echo "No courses found\n";
}
